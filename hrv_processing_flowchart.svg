<svg width="1200" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .process-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .data-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .algorithm-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .result-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-text { font-family: Arial, sans-serif; font-size: 11px; fill: white; font-weight: bold; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="title">基于rPPG的HRV心率变异性检测系统流程图</text>
  
  <!-- 1. 图像采集阶段 -->
  <rect x="50" y="60" width="200" height="60" rx="10" class="process-box"/>
  <text x="150" y="85" text-anchor="middle" class="flow-text">1. 图像采集</text>
  <text x="150" y="105" text-anchor="middle" class="flow-text">摄像头/视频流</text>
  
  <text x="270" y="85" class="text">• Base64编码图像数据</text>
  <text x="270" y="100" class="text">• 实时视频流分片上传</text>
  <text x="270" y="115" class="text">• WebM格式视频处理</text>
  
  <!-- Arrow 1 -->
  <line x1="150" y1="120" x2="150" y2="160" class="arrow"/>
  
  <!-- 2. 图像解码与预处理 -->
  <rect x="50" y="160" width="200" height="60" rx="10" class="data-box"/>
  <text x="150" y="185" text-anchor="middle" class="flow-text">2. 图像解码</text>
  <text x="150" y="205" text-anchor="middle" class="flow-text">Base64 → RGB</text>
  
  <text x="270" y="180" class="text">• base64.b64decode()</text>
  <text x="270" y="195" class="text">• PIL.Image.open()</text>
  <text x="270" y="210" class="text">• 转换为RGB格式</text>
  
  <!-- Arrow 2 -->
  <line x1="150" y1="220" x2="150" y2="260" class="arrow"/>
  
  <!-- 3. 人脸检测与定位 -->
  <rect x="50" y="260" width="200" height="80" rx="10" class="algorithm-box"/>
  <text x="150" y="285" text-anchor="middle" class="flow-text">3. 人脸检测</text>
  <text x="150" y="305" text-anchor="middle" class="flow-text">MediaPipe FaceMesh</text>
  <text x="150" y="325" text-anchor="middle" class="flow-text">ROI提取</text>
  
  <text x="270" y="275" class="text">• MediaPipe人脸网格检测</text>
  <text x="270" y="290" class="text">• 468个面部关键点</text>
  <text x="270" y="305" class="text">• 计算人脸边界框</text>
  <text x="270" y="320" class="text">• 裁剪人脸区域(ROI)</text>
  <text x="270" y="335" class="text">• 质量评估与筛选</text>
  
  <!-- Arrow 3 -->
  <line x1="150" y1="340" x2="150" y2="380" class="arrow"/>
  
  <!-- 4. RGB颜色通道提取 -->
  <rect x="50" y="380" width="200" height="80" rx="10" class="process-box"/>
  <text x="150" y="405" text-anchor="middle" class="flow-text">4. RGB通道提取</text>
  <text x="150" y="425" text-anchor="middle" class="flow-text">颜色信号分离</text>
  <text x="150" y="445" text-anchor="middle" class="flow-text">时间序列构建</text>
  
  <text x="270" y="395" class="text">• 提取R、G、B三个颜色通道</text>
  <text x="270" y="410" class="text">• 计算每帧平均颜色值</text>
  <text x="270" y="425" class="text">• 构建时间序列数据</text>
  <text x="270" y="440" class="text">• 记录时间戳信息</text>
  <text x="270" y="455" class="text">• 数据格式: [timestamp, R, G, B]</text>
  
  <!-- Arrow 4 -->
  <line x1="150" y1="460" x2="150" y2="500" class="arrow"/>
  
  <!-- 5. POS算法处理 -->
  <rect x="50" y="500" width="200" height="100" rx="10" class="algorithm-box"/>
  <text x="150" y="525" text-anchor="middle" class="flow-text">5. POS算法</text>
  <text x="150" y="545" text-anchor="middle" class="flow-text">脉搏信号提取</text>
  <text x="150" y="565" text-anchor="middle" class="flow-text">Plane Orthogonal</text>
  <text x="150" y="585" text-anchor="middle" class="flow-text">to Skin</text>
  
  <text x="270" y="515" class="text">• 时间归一化: rn=R/Rmean, gn=G/Gmean, bn=B/Bmean</text>
  <text x="270" y="530" class="text">• 投影计算: s1=gn-bn, s2=-2*rn+gn+bn</text>
  <text x="270" y="545" class="text">• 调谐处理: h=s1+std(s1)/std(s2)*s2</text>
  <text x="270" y="560" class="text">• 滑动窗口平均(winsize=45)</text>
  <text x="270" y="575" class="text">• 输出PPG原始信号</text>
  <text x="270" y="590" class="text">• 去除均值和趋势</text>
  
  <!-- Arrow 5 -->
  <line x1="150" y1="600" x2="150" y2="640" class="arrow"/>
  
  <!-- 6. 数字滤波 -->
  <rect x="50" y="640" width="200" height="80" rx="10" class="algorithm-box"/>
  <text x="150" y="665" text-anchor="middle" class="flow-text">6. 数字滤波</text>
  <text x="150" y="685" text-anchor="middle" class="flow-text">Butterworth滤波器</text>
  <text x="150" y="705" text-anchor="middle" class="flow-text">带通滤波</text>
  
  <text x="270" y="655" class="text">• Butterworth 2阶带通滤波器</text>
  <text x="270" y="670" class="text">• 截止频率: 0.8-2.5 Hz</text>
  <text x="270" y="685" class="text">• 对应心率: 48-150 BPM</text>
  <text x="270" y="700" class="text">• 去除噪声和运动伪影</text>
  <text x="270" y="715" class="text">• 保留心率相关频率成分</text>
  
  <!-- Arrow 6 -->
  <line x1="150" y1="720" x2="150" y2="760" class="arrow"/>
  
  <!-- 7. PPG信号重采样 -->
  <rect x="50" y="760" width="200" height="80" rx="10" class="process-box"/>
  <text x="150" y="785" text-anchor="middle" class="flow-text">7. 信号重采样</text>
  <text x="150" y="805" text-anchor="middle" class="flow-text">标准化采样率</text>
  <text x="150" y="825" text-anchor="middle" class="flow-text">立方插值</text>
  
  <text x="270" y="775" class="text">• 目标采样率: 30 Hz</text>
  <text x="270" y="790" class="text">• 立方插值重采样</text>
  <text x="270" y="805" class="text">• 时间轴标准化</text>
  <text x="270" y="820" class="text">• 确保信号连续性</text>
  <text x="270" y="835" class="text">• 为后续分析做准备</text>
  
  <!-- Arrow 7 -->
  <line x1="150" y1="840" x2="150" y2="880" class="arrow"/>
  
  <!-- 8. 峰值检测 -->
  <rect x="50" y="880" width="200" height="80" rx="10" class="algorithm-box"/>
  <text x="150" y="905" text-anchor="middle" class="flow-text">8. 峰值检测</text>
  <text x="150" y="925" text-anchor="middle" class="flow-text">NeuroKit2</text>
  <text x="150" y="945" text-anchor="middle" class="flow-text">PPG Peaks</text>
  
  <text x="270" y="895" class="text">• NeuroKit2.ppg_clean()信号清理</text>
  <text x="270" y="910" class="text">• NeuroKit2.ppg_peaks()峰值检测</text>
  <text x="270" y="925" class="text">• 自适应阈值算法</text>
  <text x="270" y="940" class="text">• 检测心跳峰值位置</text>
  <text x="270" y="955" class="text">• 计算峰值时间戳</text>
  
  <!-- Arrow 8 -->
  <line x1="150" y1="960" x2="150" y2="1000" class="arrow"/>
  
  <!-- 9. RR间期计算 -->
  <rect x="50" y="1000" width="200" height="80" rx="10" class="data-box"/>
  <text x="150" y="1025" text-anchor="middle" class="flow-text">9. RR间期计算</text>
  <text x="150" y="1045" text-anchor="middle" class="flow-text">心跳间隔</text>
  <text x="150" y="1065" text-anchor="middle" class="flow-text">异常值过滤</text>
  
  <text x="270" y="1015" class="text">• 计算相邻峰值时间差</text>
  <text x="270" y="1030" class="text">• RR间期 = peak_times[i+1] - peak_times[i]</text>
  <text x="270" y="1045" class="text">• 异常值过滤: 300ms < RR < 2000ms</text>
  <text x="270" y="1060" class="text">• 对应心率: 30-200 BPM</text>
  <text x="270" y="1075" class="text">• 构建RR间期时间序列</text>
  
  <!-- Arrow 9 -->
  <line x1="150" y1="1080" x2="150" y2="1120" class="arrow"/>
  
  <!-- 10. HRV分析 -->
  <rect x="50" y="1120" width="200" height="100" rx="10" class="result-box"/>
  <text x="150" y="1145" text-anchor="middle" class="flow-text">10. HRV分析</text>
  <text x="150" y="1165" text-anchor="middle" class="flow-text">心率变异性</text>
  <text x="150" y="1185" text-anchor="middle" class="flow-text">多维度指标</text>
  <text x="150" y="1205" text-anchor="middle" class="flow-text">健康评估</text>
  
  <!-- HRV指标详细说明 -->
  <text x="270" y="1135" class="subtitle">时域指标:</text>
  <text x="270" y="1150" class="text">• SDNN: RR间期标准差</text>
  <text x="270" y="1165" class="text">• RMSSD: 相邻RR间期差值均方根</text>
  <text x="270" y="1180" class="text">• pNN50: 相邻RR间期差>50ms的百分比</text>
  
  <text x="270" y="1200" class="subtitle">频域指标:</text>
  <text x="270" y="1215" class="text">• VLF: 极低频功率 (0.0033-0.04 Hz)</text>
  
  <!-- 右侧继续HRV指标 -->
  <text x="600" y="1135" class="text">• LF: 低频功率 (0.04-0.15 Hz)</text>
  <text x="600" y="1150" class="text">• HF: 高频功率 (0.15-0.4 Hz)</text>
  <text x="600" y="1165" class="text">• LF/HF: 交感/副交感神经平衡</text>
  
  <text x="600" y="1185" class="subtitle">健康评估指标:</text>
  <text x="600" y="1200" class="text">• 身体压力指数 (0-100)</text>
  <text x="600" y="1215" class="text">• 心理压力指数 (0-100)</text>
  <text x="600" y="1230" class="text">• 抗压能力指数 (0-100)</text>
  <text x="600" y="1245" class="text">• 自主神经活性与平衡性</text>
  
  <!-- 技术栈说明 -->
  <rect x="700" y="60" width="450" height="300" rx="10" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="925" y="85" text-anchor="middle" class="subtitle">核心技术栈与算法</text>
  
  <text x="720" y="110" class="subtitle">图像处理:</text>
  <text x="720" y="125" class="text">• MediaPipe FaceMesh - 人脸检测与关键点定位</text>
  <text x="720" y="140" class="text">• OpenCV - 图像处理与颜色空间转换</text>
  <text x="720" y="155" class="text">• PIL/Pillow - 图像解码与格式转换</text>
  
  <text x="720" y="180" class="subtitle">信号处理:</text>
  <text x="720" y="195" class="text">• POS算法 - 平面正交皮肤脉搏信号提取</text>
  <text x="720" y="210" class="text">• Butterworth滤波器 - 带通滤波去噪</text>
  <text x="720" y="225" class="text">• SciPy插值 - 立方插值重采样</text>
  
  <text x="720" y="250" class="subtitle">生理信号分析:</text>
  <text x="720" y="265" class="text">• NeuroKit2 - PPG信号清理与峰值检测</text>
  <text x="720" y="280" class="text">• HRV分析 - 时域、频域多维度指标计算</text>
  <text x="720" y="295" class="text">• Welch功率谱密度 - 频域分析</text>
  
  <text x="720" y="320" class="subtitle">数据处理:</text>
  <text x="720" y="335" class="text">• NumPy - 数值计算与数组操作</text>
  <text x="720" y="350" class="text">• Pandas - 数据结构与时间序列处理</text>
  
  <!-- 算法原理说明 -->
  <rect x="700" y="380" width="450" height="280" rx="10" fill="#fdf2e9" stroke="#e67e22" stroke-width="1"/>
  <text x="925" y="405" text-anchor="middle" class="subtitle">rPPG原理与POS算法详解</text>
  
  <text x="720" y="430" class="subtitle">rPPG基本原理:</text>
  <text x="720" y="445" class="text">• 心脏泵血导致面部血管容积周期性变化</text>
  <text x="720" y="460" class="text">• 血液吸收特定波长光线，影响皮肤反射光强度</text>
  <text x="720" y="475" class="text">• 摄像头捕获的RGB信号包含脉搏信息</text>
  
  <text x="720" y="500" class="subtitle">POS算法核心:</text>
  <text x="720" y="515" class="text">• 假设皮肤反射模型: I = I₀ + I₁·cos(ωt + φ)</text>
  <text x="720" y="530" class="text">• 时间归一化消除光照变化影响</text>
  <text x="720" y="545" class="text">• 投影到正交平面分离脉搏信号</text>
  <text x="720" y="560" class="text">• 自适应调谐优化信噪比</text>
  
  <text x="720" y="585" class="subtitle">HRV生理意义:</text>
  <text x="720" y="600" class="text">• 反映自主神经系统调节能力</text>
  <text x="720" y="615" class="text">• 交感神经↑ → HRV↓ → 压力↑</text>
  <text x="720" y="630" class="text">• 副交感神经↑ → HRV↑ → 放松状态</text>
  <text x="720" y="645" class="text">• 可用于压力监测、健康评估</text>
  
  <!-- 数据流向箭头 -->
  <path d="M 250 90 Q 350 90 450 90 Q 550 90 650 90 Q 700 90 700 200" class="arrow"/>
  <text x="475" y="85" class="small-text">实时数据流</text>
  
  <!-- 反馈箭头 -->
  <path d="M 700 600 Q 650 650 600 700 Q 550 750 500 800 Q 450 850 400 900 Q 350 950 300 1000" class="arrow"/>
  <text x="500" y="750" class="small-text">算法优化反馈</text>
  
  <!-- 版权信息 -->
  <text x="600" y="1380" text-anchor="middle" class="small-text">基于MediaPipe + POS算法 + NeuroKit2的rPPG-HRV检测系统</text>
</svg>
