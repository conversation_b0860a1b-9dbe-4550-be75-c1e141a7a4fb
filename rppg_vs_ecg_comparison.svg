<svg width="1400" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .rppg-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .ecg-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .comparison-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .result-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-text { font-family: Arial, sans-serif; font-size: 11px; fill: white; font-weight: bold; }
      .signal-line { stroke-width: 2; fill: none; }
      .ppg-signal { stroke: #3498db; }
      .ecg-signal { stroke: #e74c3c; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">人脸rPPG心率检测 vs 心电图ECG对比分析</text>
  
  <!-- Left Side: rPPG Process -->
  <text x="200" y="70" text-anchor="middle" class="subtitle">rPPG (远程光电容积脉搏波)</text>
  
  <!-- 1. rPPG Data Source -->
  <rect x="50" y="90" width="300" height="60" rx="10" class="rppg-box"/>
  <text x="200" y="115" text-anchor="middle" class="flow-text">人脸视频采集</text>
  <text x="200" y="135" text-anchor="middle" class="flow-text">RGB颜色通道提取</text>
  
  <!-- 2. rPPG Signal Processing -->
  <rect x="50" y="170" width="300" height="80" rx="10" class="rppg-box"/>
  <text x="200" y="195" text-anchor="middle" class="flow-text">POS算法处理</text>
  <text x="200" y="215" text-anchor="middle" class="flow-text">数字滤波 (0.8-2.5 Hz)</text>
  <text x="200" y="235" text-anchor="middle" class="flow-text">信号重采样 (30 Hz)</text>
  
  <!-- 3. rPPG Peak Detection -->
  <rect x="50" y="270" width="300" height="60" rx="10" class="rppg-box"/>
  <text x="200" y="295" text-anchor="middle" class="flow-text">PPG峰值检测</text>
  <text x="200" y="315" text-anchor="middle" class="flow-text">NeuroKit2算法</text>
  
  <!-- Right Side: ECG Process -->
  <text x="900" y="70" text-anchor="middle" class="subtitle">ECG (心电图)</text>
  
  <!-- 1. ECG Data Source -->
  <rect x="750" y="90" width="300" height="60" rx="10" class="ecg-box"/>
  <text x="900" y="115" text-anchor="middle" class="flow-text">心电图设备采集</text>
  <text x="900" y="135" text-anchor="middle" class="flow-text">电极接触皮肤</text>
  
  <!-- 2. ECG Signal Processing -->
  <rect x="750" y="170" width="300" height="80" rx="10" class="ecg-box"/>
  <text x="900" y="195" text-anchor="middle" class="flow-text">ECG信号预处理</text>
  <text x="900" y="215" text-anchor="middle" class="flow-text">基线漂移校正</text>
  <text x="900" y="235" text-anchor="middle" class="flow-text">噪声滤波</text>
  
  <!-- 3. ECG Peak Detection -->
  <rect x="750" y="270" width="300" height="60" rx="10" class="ecg-box"/>
  <text x="900" y="295" text-anchor="middle" class="flow-text">R波峰值检测</text>
  <text x="900" y="315" text-anchor="middle" class="flow-text">QRS复合波识别</text>
  
  <!-- Signal Visualization -->
  <rect x="50" y="360" width="1300" height="200" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="700" y="385" text-anchor="middle" class="subtitle">信号波形对比</text>
  
  <!-- PPG Signal -->
  <text x="70" y="410" class="text">rPPG信号 (从人脸RGB提取):</text>
  <path d="M 70 430 Q 120 420 170 430 Q 220 440 270 430 Q 320 420 370 430 Q 420 440 470 430 Q 520 420 570 430" class="signal-line ppg-signal"/>
  <circle cx="120" cy="420" r="3" fill="#3498db"/>
  <circle cx="270" cy="430" r="3" fill="#3498db"/>
  <circle cx="420" cy="440" r="3" fill="#3498db"/>
  <circle cx="570" cy="430" r="3" fill="#3498db"/>
  <text x="580" y="435" class="small-text">PPG峰值</text>
  
  <!-- ECG Signal -->
  <text x="70" y="480" class="text">ECG信号 (心电图):</text>
  <path d="M 70 500 L 110 500 L 120 480 L 130 520 L 140 460 L 150 500 L 190 500 L 220 500 L 230 480 L 240 520 L 250 460 L 260 500 L 300 500 L 330 500 L 340 480 L 350 520 L 360 460 L 370 500 L 410 500 L 440 500 L 450 480 L 460 520 L 470 460 L 480 500 L 520 500" class="signal-line ecg-signal"/>
  <circle cx="130" cy="460" r="3" fill="#e74c3c"/>
  <circle cx="250" cy="460" r="3" fill="#e74c3c"/>
  <circle cx="360" cy="460" r="3" fill="#e74c3c"/>
  <circle cx="470" cy="460" r="3" fill="#e74c3c"/>
  <text x="530" y="465" class="small-text">R波峰值</text>
  
  <!-- Heart Rate Calculation -->
  <text x="700" y="530" text-anchor="middle" class="subtitle">心率计算公式</text>
  <text x="700" y="550" text-anchor="middle" class="text">心率 (BPM) = 60000 / 平均RR间期(ms)</text>
  
  <!-- Arrows -->
  <line x1="200" y1="150" x2="200" y2="170" class="arrow"/>
  <line x1="200" y1="250" x2="200" y2="270" class="arrow"/>
  <line x1="900" y1="150" x2="900" y2="170" class="arrow"/>
  <line x1="900" y1="250" x2="900" y2="270" class="arrow"/>
  
  <!-- Convergence arrows -->
  <line x1="200" y1="330" x2="400" y2="380" class="arrow"/>
  <line x1="900" y1="330" x2="700" y2="380" class="arrow"/>
  
  <!-- Comparison Section -->
  <rect x="50" y="580" width="1300" height="280" rx="10" class="comparison-box"/>
  <text x="700" y="610" text-anchor="middle" class="flow-text">对比分析与验证</text>
  
  <!-- Comparison Table -->
  <rect x="80" y="630" width="600" height="220" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="380" y="655" text-anchor="middle" class="subtitle">技术特性对比</text>
  
  <!-- Table Headers -->
  <line x1="80" y1="670" x2="680" y2="670" stroke="#bdc3c7" stroke-width="1"/>
  <text x="120" y="690" class="text">特性</text>
  <text x="300" y="690" class="text">rPPG</text>
  <text x="550" y="690" class="text">ECG</text>
  
  <!-- Table Rows -->
  <line x1="80" y1="700" x2="680" y2="700" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="720" class="text">接触方式</text>
  <text x="300" y="720" class="text">非接触</text>
  <text x="550" y="720" class="text">接触式</text>
  
  <line x1="80" y1="730" x2="680" y2="730" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="750" class="text">设备要求</text>
  <text x="300" y="750" class="text">普通摄像头</text>
  <text x="550" y="750" class="text">专业ECG设备</text>
  
  <line x1="80" y1="760" x2="680" y2="760" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="780" class="text">精度</text>
  <text x="300" y="780" class="text">中等 (±5 BPM)</text>
  <text x="550" y="780" class="text">高 (±1 BPM)</text>
  
  <line x1="80" y1="790" x2="680" y2="790" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="810" class="text">便携性</text>
  <text x="300" y="810" class="text">极高</text>
  <text x="550" y="810" class="text">中等</text>
  
  <line x1="80" y1="820" x2="680" y2="820" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="840" class="text">成本</text>
  <text x="300" y="840" class="text">低</text>
  <text x="550" y="840" class="text">高</text>
  
  <!-- Validation Methods -->
  <rect x="720" y="630" width="600" height="220" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="1020" y="655" text-anchor="middle" class="subtitle">验证方法</text>
  
  <text x="740" y="680" class="text">1. 同步采集对比:</text>
  <text x="760" y="700" class="text">• 同时记录rPPG和ECG信号</text>
  <text x="760" y="715" class="text">• 计算心率相关性系数</text>
  <text x="760" y="730" class="text">• Bland-Altman一致性分析</text>
  
  <text x="740" y="755" class="text">2. 统计指标:</text>
  <text x="760" y="775" class="text">• 平均绝对误差 (MAE)</text>
  <text x="760" y="790" class="text">• 均方根误差 (RMSE)</text>
  <text x="760" y="805" class="text">• 皮尔逊相关系数 (r)</text>
  
  <text x="740" y="830" class="text">3. 临床验证:</text>
  <text x="760" y="845" class="text">• 不同年龄组测试</text>
  
  <!-- Results Section -->
  <rect x="50" y="880" width="1300" height="280" rx="10" class="result-box"/>
  <text x="700" y="910" text-anchor="middle" class="flow-text">心率计算结果与应用</text>
  
  <!-- Heart Rate Calculation Details -->
  <rect x="80" y="930" width="580" height="220" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="370" y="955" text-anchor="middle" class="subtitle">心率计算详细过程</text>
  
  <text x="100" y="980" class="text">1. RR间期提取:</text>
  <text x="120" y="1000" class="text">• 检测相邻峰值时间差</text>
  <text x="120" y="1015" class="text">• RR_interval = peak_time[i+1] - peak_time[i]</text>
  <text x="120" y="1030" class="text">• 异常值过滤: 300ms < RR < 2000ms</text>
  
  <text x="100" y="1055" class="text">2. 心率计算:</text>
  <text x="120" y="1075" class="text">• 瞬时心率 = 60000 / RR_interval (ms)</text>
  <text x="120" y="1090" class="text">• 平均心率 = 60000 / mean(RR_intervals)</text>
  <text x="120" y="1105" class="text">• 心率范围: 30-200 BPM</text>
  
  <text x="100" y="1130" class="text">3. 质量控制:</text>
  <text x="120" y="1145" class="text">• 信号质量评估 (SNR > 10dB)</text>
  
  <!-- Applications -->
  <rect x="720" y="930" width="580" height="220" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="1010" y="955" text-anchor="middle" class="subtitle">应用场景</text>
  
  <text x="740" y="980" class="text">rPPG心率检测应用:</text>
  <text x="760" y="1000" class="text">• 远程健康监测</text>
  <text x="760" y="1015" class="text">• 智能手机/平板应用</text>
  <text x="760" y="1030" class="text">• 视频会议健康监测</text>
  <text x="760" y="1045" class="text">• 婴幼儿非接触监测</text>
  
  <text x="740" y="1070" class="text">ECG心率检测应用:</text>
  <text x="760" y="1090" class="text">• 医院临床诊断</text>
  <text x="760" y="1105" class="text">• 心脏病监测</text>
  <text x="760" y="1120" class="text">• 运动心率监测</text>
  <text x="760" y="1135" class="text">• 精确医疗诊断</text>
  
  <!-- Technical Notes -->
  <text x="700" y="1180" text-anchor="middle" class="small-text">注: rPPG技术基于血液容积变化引起的光学特性变化，通过POS算法从RGB信号中提取脉搏信息</text>
</svg>
