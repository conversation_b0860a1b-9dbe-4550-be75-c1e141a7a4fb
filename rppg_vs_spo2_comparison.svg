<svg width="1400" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .rppg-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .spo2-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .comparison-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .result-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .principle-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-text { font-family: Arial, sans-serif; font-size: 11px; fill: white; font-weight: bold; }
      .signal-line { stroke-width: 2; fill: none; }
      .ppg-signal { stroke: #3498db; }
      .spo2-signal { stroke: #e74c3c; }
      .red-light { stroke: #ff0000; }
      .infrared-light { stroke: #800080; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">rPPG (远程光电容积脉搏波) vs 血氧饱和度 (SpO2) 对比分析</text>
  
  <!-- Left Side: rPPG -->
  <text x="350" y="70" text-anchor="middle" class="subtitle">rPPG - 远程光电容积脉搏波</text>
  
  <!-- rPPG Principle -->
  <rect x="50" y="90" width="600" height="120" rx="10" class="rppg-box"/>
  <text x="350" y="115" text-anchor="middle" class="flow-text">检测原理: 血管容积变化</text>
  <text x="70" y="140" class="flow-text">• 心脏跳动 → 血管容积周期性变化</text>
  <text x="70" y="160" class="flow-text">• 血液吸收光线 → 皮肤反射光强度变化</text>
  <text x="70" y="180" class="flow-text">• RGB摄像头捕获 → 提取脉搏信号</text>
  <text x="70" y="200" class="flow-text">• 单一光源 → 主要使用绿光通道</text>
  
  <!-- Right Side: SpO2 -->
  <text x="1050" y="70" text-anchor="middle" class="subtitle">SpO2 - 血氧饱和度</text>
  
  <!-- SpO2 Principle -->
  <rect x="750" y="90" width="600" height="120" rx="10" class="spo2-box"/>
  <text x="1050" y="115" text-anchor="middle" class="flow-text">检测原理: 血红蛋白光谱特性</text>
  <text x="770" y="140" class="flow-text">• 氧合血红蛋白(HbO2) vs 脱氧血红蛋白(Hb)</text>
  <text x="770" y="160" class="flow-text">• 双波长光源: 红光(660nm) + 红外光(940nm)</text>
  <text x="770" y="180" class="flow-text">• 不同血红蛋白对光的吸收差异</text>
  <text x="770" y="200" class="flow-text">• 计算氧饱和度百分比</text>
  
  <!-- Signal Processing Comparison -->
  <rect x="50" y="240" width="1300" height="200" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="700" y="265" text-anchor="middle" class="subtitle">信号处理对比</text>
  
  <!-- rPPG Signal Processing -->
  <text x="70" y="290" class="text">rPPG信号处理流程:</text>
  <rect x="70" y="300" width="580" height="130" rx="5" fill="white" stroke="#3498db" stroke-width="1"/>
  <text x="90" y="320" class="text">1. RGB图像采集 → 人脸检测 → ROI提取</text>
  <text x="90" y="340" class="text">2. 颜色通道分离 → POS算法处理</text>
  <text x="90" y="360" class="text">3. 数字滤波 (0.8-2.5 Hz) → 峰值检测</text>
  <text x="90" y="380" class="text">4. RR间期计算 → 心率 + HRV分析</text>
  <text x="90" y="400" class="text">输出: 心率(BPM), HRV指标, 压力评估</text>
  <text x="90" y="420" class="text">精度: ±3-5 BPM</text>
  
  <!-- SpO2 Signal Processing -->
  <text x="770" y="290" class="text">SpO2信号处理流程:</text>
  <rect x="770" y="300" width="580" height="130" rx="5" fill="white" stroke="#e74c3c" stroke-width="1"/>
  <text x="790" y="320" class="text">1. 双波长LED发光 → 光电检测器接收</text>
  <text x="790" y="340" class="text">2. AC/DC分量分离 → 脉搏波提取</text>
  <text x="790" y="360" class="text">3. 红光/红外光比值计算 (R值)</text>
  <text x="790" y="380" class="text">4. 查表法或经验公式 → SpO2计算</text>
  <text x="790" y="400" class="text">输出: 血氧饱和度(%), 脉率(BPM)</text>
  <text x="790" y="420" class="text">精度: ±2% (SpO2), ±3 BPM (脉率)</text>
  
  <!-- Technical Principles -->
  <rect x="50" y="460" width="1300" height="280" rx="10" class="principle-box"/>
  <text x="700" y="485" text-anchor="middle" class="flow-text">技术原理详解</text>
  
  <!-- rPPG Technical Details -->
  <rect x="80" y="510" width="580" height="220" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="370" y="535" text-anchor="middle" class="subtitle">rPPG技术原理</text>
  
  <text x="100" y="560" class="text">光学基础:</text>
  <text x="120" y="580" class="text">• Beer-Lambert定律: I = I₀ × e^(-εcl)</text>
  <text x="120" y="600" class="text">• 血液容积变化 → 光程长度变化</text>
  <text x="120" y="620" class="text">• 主要使用绿光 (540nm) - 血红蛋白吸收峰</text>
  
  <text x="100" y="650" class="text">POS算法核心:</text>
  <text x="120" y="670" class="text">• 时间归一化: 消除光照变化</text>
  <text x="120" y="690" class="text">• 投影分离: s1=G-B, s2=-2R+G+B</text>
  <text x="120" y="710" class="text">• 自适应调谐: 优化信噪比</text>
  
  <!-- SpO2 Technical Details -->
  <rect x="720" y="510" width="580" height="220" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="1010" y="535" text-anchor="middle" class="subtitle">SpO2技术原理</text>
  
  <text x="740" y="560" class="text">光谱学基础:</text>
  <text x="760" y="580" class="text">• HbO2在660nm处吸收低，940nm处吸收高</text>
  <text x="760" y="600" class="text">• Hb在660nm处吸收高，940nm处吸收低</text>
  <text x="760" y="620" class="text">• 等吸收点: 805nm (两者吸收相等)</text>
  
  <text x="740" y="650" class="text">计算公式:</text>
  <text x="760" y="670" class="text">• R = (AC₆₆₀/DC₆₆₀) / (AC₉₄₀/DC₉₄₀)</text>
  <text x="760" y="690" class="text">• SpO2 = f(R) - 经验公式或查表</text>
  <text x="760" y="710" class="text">• 典型: SpO2 = 110 - 25×R</text>
  
  <!-- Comparison Table -->
  <rect x="50" y="760" width="1300" height="320" rx="10" class="comparison-box"/>
  <text x="700" y="785" text-anchor="middle" class="flow-text">详细对比分析</text>
  
  <rect x="80" y="810" width="1240" height="260" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  
  <!-- Table Headers -->
  <text x="200" y="835" text-anchor="middle" class="subtitle">对比项目</text>
  <text x="500" y="835" text-anchor="middle" class="subtitle">rPPG</text>
  <text x="900" y="835" text-anchor="middle" class="subtitle">SpO2</text>
  
  <line x1="80" y1="845" x2="1320" y2="845" stroke="#bdc3c7" stroke-width="1"/>
  
  <!-- Table Rows -->
  <text x="120" y="865" class="text">检测目标</text>
  <text x="350" y="865" class="text">心率、心率变异性、压力状态</text>
  <text x="750" y="865" class="text">血氧饱和度、脉率</text>
  
  <line x1="80" y1="875" x2="1320" y2="875" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="895" class="text">光源要求</text>
  <text x="350" y="895" class="text">环境光或单色光 (主要绿光)</text>
  <text x="750" y="895" class="text">双波长LED (660nm红光+940nm红外)</text>
  
  <line x1="80" y1="905" x2="1320" y2="905" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="925" class="text">检测方式</text>
  <text x="350" y="925" class="text">反射式 (皮肤表面反射)</text>
  <text x="750" y="925" class="text">透射式 (指尖/耳垂透射)</text>
  
  <line x1="80" y1="935" x2="1320" y2="935" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="955" class="text">接触方式</text>
  <text x="350" y="955" class="text">非接触 (摄像头距离30-60cm)</text>
  <text x="750" y="955" class="text">接触式 (传感器贴合皮肤)</text>
  
  <line x1="80" y1="965" x2="1320" y2="965" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="985" class="text">设备成本</text>
  <text x="350" y="985" class="text">低 (普通摄像头)</text>
  <text x="750" y="985" class="text">中等 (专用传感器)</text>
  
  <line x1="80" y1="995" x2="1320" y2="995" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="1015" class="text">便携性</text>
  <text x="350" y="1015" class="text">极高 (手机/平板集成)</text>
  <text x="750" y="1015" class="text">高 (小型化设备)</text>
  
  <line x1="80" y1="1025" x2="1320" y2="1025" stroke="#ecf0f1" stroke-width="1"/>
  <text x="120" y="1045" class="text">测量精度</text>
  <text x="350" y="1045" class="text">心率: ±3-5 BPM</text>
  <text x="750" y="1045" class="text">SpO2: ±2%, 脉率: ±3 BPM</text>
  
  <!-- Applications -->
  <rect x="50" y="1100" width="1300" height="280" rx="10" class="result-box"/>
  <text x="700" y="1125" text-anchor="middle" class="flow-text">应用场景与发展趋势</text>
  
  <!-- rPPG Applications -->
  <rect x="80" y="1150" width="580" height="220" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="370" y="1175" text-anchor="middle" class="subtitle">rPPG应用场景</text>
  
  <text x="100" y="1200" class="text">当前应用:</text>
  <text x="120" y="1220" class="text">• 远程健康监测 (视频通话)</text>
  <text x="120" y="1240" class="text">• 压力状态评估</text>
  <text x="120" y="1260" class="text">• 婴幼儿非接触监测</text>
  <text x="120" y="1280" class="text">• 智能手机健康应用</text>
  
  <text x="100" y="1310" class="text">发展趋势:</text>
  <text x="120" y="1330" class="text">• 多光谱rPPG (血氧检测)</text>
  <text x="120" y="1350" class="text">• AI算法优化精度</text>
  
  <!-- SpO2 Applications -->
  <rect x="720" y="1150" width="580" height="220" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="1010" y="1175" text-anchor="middle" class="subtitle">SpO2应用场景</text>
  
  <text x="740" y="1200" class="text">医疗应用:</text>
  <text x="760" y="1220" class="text">• 呼吸系统疾病监测</text>
  <text x="760" y="1240" class="text">• 睡眠呼吸暂停检测</text>
  <text x="760" y="1260" class="text">• 重症监护连续监测</text>
  <text x="760" y="1280" class="text">• COVID-19等疾病筛查</text>
  
  <text x="740" y="1310" class="text">消费级应用:</text>
  <text x="760" y="1330" class="text">• 智能手表/手环</text>
  <text x="760" y="1350" class="text">• 运动健身监测</text>
  
  <!-- Arrows showing relationship -->
  <line x1="350" y1="210" x2="350" y2="240" class="arrow"/>
  <line x1="1050" y1="210" x2="1050" y2="240" class="arrow"/>
  
  <!-- Technical Notes -->
  <text x="700" y="1390" text-anchor="middle" class="small-text">注: rPPG技术正在向多光谱方向发展，未来可能实现非接触式血氧检测</text>
</svg>
