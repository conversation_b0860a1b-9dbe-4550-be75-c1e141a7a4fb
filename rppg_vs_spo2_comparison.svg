<svg width="1600" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .rppg-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .spo2-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .radar-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .comparison-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .result-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .principle-box { fill: #34495e; stroke: #2c3e50; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .flow-text { font-family: Arial, sans-serif; font-size: 11px; fill: white; font-weight: bold; }
      .signal-line { stroke-width: 2; fill: none; }
      .ppg-signal { stroke: #3498db; }
      .spo2-signal { stroke: #e74c3c; }
      .radar-signal { stroke: #9b59b6; }
      .red-light { stroke: #ff0000; }
      .infrared-light { stroke: #800080; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="800" y="30" text-anchor="middle" class="title">心率变异性检测技术三方对比: rPPG vs SpO2 vs 射频雷达</text>
  
  <!-- Three columns layout -->
  <!-- Left: rPPG -->
  <text x="250" y="70" text-anchor="middle" class="subtitle">rPPG - 远程光电容积脉搏波</text>

  <!-- rPPG Principle -->
  <rect x="50" y="90" width="400" height="120" rx="10" class="rppg-box"/>
  <text x="250" y="115" text-anchor="middle" class="flow-text">检测原理: 血管容积变化</text>
  <text x="70" y="140" class="flow-text">• 心脏跳动 → 血管容积周期性变化</text>
  <text x="70" y="160" class="flow-text">• 血液吸收光线 → 皮肤反射光强度变化</text>
  <text x="70" y="180" class="flow-text">• RGB摄像头捕获 → 提取脉搏信号</text>
  <text x="70" y="200" class="flow-text">• 单一光源 → 主要使用绿光通道</text>

  <!-- Middle: SpO2 -->
  <text x="650" y="70" text-anchor="middle" class="subtitle">SpO2 - 血氧饱和度</text>

  <!-- SpO2 Principle -->
  <rect x="500" y="90" width="400" height="120" rx="10" class="spo2-box"/>
  <text x="650" y="115" text-anchor="middle" class="flow-text">检测原理: 血红蛋白光谱特性</text>
  <text x="520" y="140" class="flow-text">• 氧合血红蛋白(HbO2) vs 脱氧血红蛋白(Hb)</text>
  <text x="520" y="160" class="flow-text">• 双波长光源: 红光(660nm) + 红外光(940nm)</text>
  <text x="520" y="180" class="flow-text">• 不同血红蛋白对光的吸收差异</text>
  <text x="520" y="200" class="flow-text">• 计算氧饱和度百分比</text>

  <!-- Right: FMCW Radar -->
  <text x="1150" y="70" text-anchor="middle" class="subtitle">FMCW雷达 - 射频心率检测</text>

  <!-- Radar Principle -->
  <rect x="950" y="90" width="400" height="120" rx="10" class="radar-box"/>
  <text x="1150" y="115" text-anchor="middle" class="flow-text">检测原理: 多普勒效应</text>
  <text x="970" y="140" class="flow-text">• 胸壁微动 → 雷达反射信号变化</text>
  <text x="970" y="160" class="flow-text">• FMCW调频连续波 → 距离-多普勒分析</text>
  <text x="970" y="180" class="flow-text">• 微多普勒信号提取 → 心跳呼吸分离</text>
  <text x="970" y="200" class="flow-text">• 毫米波频段 → 高精度非接触检测</text>
  
  <!-- Signal Processing Comparison -->
  <rect x="50" y="240" width="1500" height="220" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="800" y="265" text-anchor="middle" class="subtitle">信号处理对比</text>

  <!-- rPPG Signal Processing -->
  <text x="70" y="290" class="text">rPPG信号处理流程:</text>
  <rect x="70" y="300" width="380" height="150" rx="5" fill="white" stroke="#3498db" stroke-width="1"/>
  <text x="90" y="320" class="text">1. RGB图像采集 → 人脸检测</text>
  <text x="90" y="340" class="text">2. ROI提取 → POS算法处理</text>
  <text x="90" y="360" class="text">3. 数字滤波 (0.8-2.5 Hz)</text>
  <text x="90" y="380" class="text">4. 峰值检测 → RR间期计算</text>
  <text x="90" y="400" class="text">5. HRV分析 → 压力评估</text>
  <text x="90" y="420" class="text">输出: 心率, HRV指标</text>
  <text x="90" y="440" class="text">精度: ±3-5 BPM</text>

  <!-- SpO2 Signal Processing -->
  <text x="520" y="290" class="text">SpO2信号处理流程:</text>
  <rect x="520" y="300" width="380" height="150" rx="5" fill="white" stroke="#e74c3c" stroke-width="1"/>
  <text x="540" y="320" class="text">1. 双波长LED发光</text>
  <text x="540" y="340" class="text">2. 光电检测器接收</text>
  <text x="540" y="360" class="text">3. AC/DC分量分离</text>
  <text x="540" y="380" class="text">4. 红光/红外光比值计算</text>
  <text x="540" y="400" class="text">5. 查表法 → SpO2计算</text>
  <text x="540" y="420" class="text">输出: 血氧饱和度, 脉率</text>
  <text x="540" y="440" class="text">精度: ±2% (SpO2)</text>

  <!-- Radar Signal Processing -->
  <text x="970" y="290" class="text">FMCW雷达信号处理流程:</text>
  <rect x="970" y="300" width="380" height="150" rx="5" fill="white" stroke="#9b59b6" stroke-width="1"/>
  <text x="990" y="320" class="text">1. FMCW信号发射接收</text>
  <text x="990" y="340" class="text">2. 距离-多普勒FFT</text>
  <text x="990" y="360" class="text">3. 微多普勒信号提取</text>
  <text x="990" y="380" class="text">4. 心跳呼吸信号分离</text>
  <text x="990" y="400" class="text">5. 峰值检测 → HRV分析</text>
  <text x="990" y="420" class="text">输出: 心率, 呼吸率, HRV</text>
  <text x="990" y="440" class="text">精度: ±2-4 BPM</text>
  
  <!-- Technical Principles -->
  <rect x="50" y="480" width="1500" height="300" rx="10" class="principle-box"/>
  <text x="800" y="505" text-anchor="middle" class="flow-text">技术原理详解</text>

  <!-- rPPG Technical Details -->
  <rect x="80" y="530" width="380" height="240" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="270" y="555" text-anchor="middle" class="subtitle">rPPG技术原理</text>

  <text x="100" y="580" class="text">光学基础:</text>
  <text x="120" y="600" class="text">• Beer-Lambert定律</text>
  <text x="120" y="620" class="text">• 血液容积变化 → 光程变化</text>
  <text x="120" y="640" class="text">• 绿光 (540nm) 强吸收</text>

  <text x="100" y="670" class="text">POS算法核心:</text>
  <text x="120" y="690" class="text">• 时间归一化</text>
  <text x="120" y="710" class="text">• 投影分离: s1=G-B</text>
  <text x="120" y="730" class="text">• 自适应调谐</text>
  <text x="120" y="750" class="text">• 信噪比优化</text>

  <!-- SpO2 Technical Details -->
  <rect x="510" y="530" width="380" height="240" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="700" y="555" text-anchor="middle" class="subtitle">SpO2技术原理</text>

  <text x="530" y="580" class="text">光谱学基础:</text>
  <text x="550" y="600" class="text">• HbO2 vs Hb吸收差异</text>
  <text x="550" y="620" class="text">• 660nm红光 + 940nm红外</text>
  <text x="550" y="640" class="text">• 等吸收点: 805nm</text>

  <text x="530" y="670" class="text">计算公式:</text>
  <text x="550" y="690" class="text">• R = (AC₆₆₀/DC₆₆₀)</text>
  <text x="550" y="710" class="text">    / (AC₉₄₀/DC₉₄₀)</text>
  <text x="550" y="730" class="text">• SpO2 = f(R)</text>
  <text x="550" y="750" class="text">• 经验公式或查表</text>

  <!-- FMCW Radar Technical Details -->
  <rect x="940" y="530" width="380" height="240" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="1130" y="555" text-anchor="middle" class="subtitle">FMCW雷达原理</text>

  <text x="960" y="580" class="text">多普勒效应:</text>
  <text x="980" y="600" class="text">• 胸壁微动 → 频率偏移</text>
  <text x="980" y="620" class="text">• fd = 2v/λ × cosθ</text>
  <text x="980" y="640" class="text">• 毫米波: 60-77 GHz</text>

  <text x="960" y="670" class="text">信号处理:</text>
  <text x="980" y="690" class="text">• Range-Doppler FFT</text>
  <text x="980" y="710" class="text">• 微多普勒提取</text>
  <text x="980" y="730" class="text">• 心跳呼吸分离</text>
  <text x="980" y="750" class="text">• VMD/EMD分解</text>
  
  <!-- Comparison Table -->
  <rect x="50" y="800" width="1500" height="380" rx="10" class="comparison-box"/>
  <text x="800" y="825" text-anchor="middle" class="flow-text">详细对比分析</text>

  <rect x="80" y="850" width="1440" height="320" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>

  <!-- Table Headers -->
  <text x="180" y="875" text-anchor="middle" class="subtitle">对比项目</text>
  <text x="380" y="875" text-anchor="middle" class="subtitle">rPPG</text>
  <text x="680" y="875" text-anchor="middle" class="subtitle">SpO2</text>
  <text x="1080" y="875" text-anchor="middle" class="subtitle">FMCW雷达</text>

  <line x1="80" y1="885" x2="1520" y2="885" stroke="#bdc3c7" stroke-width="1"/>

  <!-- Table Rows -->
  <text x="100" y="905" class="text">检测目标</text>
  <text x="280" y="905" class="text">心率、HRV、压力状态</text>
  <text x="580" y="905" class="text">血氧饱和度、脉率</text>
  <text x="980" y="905" class="text">心率、HRV、呼吸率</text>

  <line x1="80" y1="915" x2="1520" y2="915" stroke="#ecf0f1" stroke-width="1"/>
  <text x="100" y="935" class="text">信号源</text>
  <text x="280" y="935" class="text">环境光/绿光LED</text>
  <text x="580" y="935" class="text">双波长LED</text>
  <text x="980" y="935" class="text">毫米波雷达</text>

  <line x1="80" y1="945" x2="1520" y2="945" stroke="#ecf0f1" stroke-width="1"/>
  <text x="100" y="965" class="text">检测方式</text>
  <text x="280" y="965" class="text">反射式光学</text>
  <text x="580" y="965" class="text">透射式光学</text>
  <text x="980" y="965" class="text">射频反射</text>

  <line x1="80" y1="975" x2="1520" y2="975" stroke="#ecf0f1" stroke-width="1"/>
  <text x="100" y="995" class="text">接触方式</text>
  <text x="280" y="995" class="text">非接触 (30-60cm)</text>
  <text x="580" y="995" class="text">接触式</text>
  <text x="980" y="995" class="text">非接触 (1-3m)</text>

  <line x1="80" y1="1005" x2="1520" y2="1005" stroke="#ecf0f1" stroke-width="1"/>
  <text x="100" y="1025" class="text">设备成本</text>
  <text x="280" y="1025" class="text">低 (摄像头)</text>
  <text x="580" y="1025" class="text">中等 (传感器)</text>
  <text x="980" y="1025" class="text">高 (雷达芯片)</text>

  <line x1="80" y1="1035" x2="1520" y2="1035" stroke="#ecf0f1" stroke-width="1"/>
  <text x="100" y="1055" class="text">便携性</text>
  <text x="280" y="1055" class="text">极高 (手机集成)</text>
  <text x="580" y="1055" class="text">高 (小型设备)</text>
  <text x="980" y="1055" class="text">中等 (专用设备)</text>

  <line x1="80" y1="1065" x2="1520" y2="1065" stroke="#ecf0f1" stroke-width="1"/>
  <text x="100" y="1085" class="text">测量精度</text>
  <text x="280" y="1085" class="text">心率: ±3-5 BPM</text>
  <text x="580" y="1085" class="text">SpO2: ±2%</text>
  <text x="980" y="1085" class="text">心率: ±2-4 BPM</text>

  <line x1="80" y1="1095" x2="1520" y2="1095" stroke="#ecf0f1" stroke-width="1"/>
  <text x="100" y="1115" class="text">环境适应性</text>
  <text x="280" y="1115" class="text">光照敏感</text>
  <text x="580" y="1115" class="text">运动敏感</text>
  <text x="980" y="1115" class="text">环境鲁棒性强</text>

  <line x1="80" y1="1125" x2="1520" y2="1125" stroke="#ecf0f1" stroke-width="1"/>
  <text x="100" y="1145" class="text">多目标检测</text>
  <text x="280" y="1145" class="text">单人 (人脸ROI)</text>
  <text x="580" y="1145" class="text">单人 (接触点)</text>
  <text x="980" y="1145" class="text">多人 (空间分离)</text>
  
  <!-- Applications -->
  <rect x="50" y="1200" width="1500" height="320" rx="10" class="result-box"/>
  <text x="800" y="1225" text-anchor="middle" class="flow-text">应用场景与发展趋势</text>

  <!-- rPPG Applications -->
  <rect x="80" y="1250" width="380" height="260" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="270" y="1275" text-anchor="middle" class="subtitle">rPPG应用场景</text>

  <text x="100" y="1300" class="text">当前应用:</text>
  <text x="120" y="1320" class="text">• 远程健康监测</text>
  <text x="120" y="1340" class="text">• 压力状态评估</text>
  <text x="120" y="1360" class="text">• 婴幼儿监测</text>
  <text x="120" y="1380" class="text">• 智能手机应用</text>

  <text x="100" y="1410" class="text">发展趋势:</text>
  <text x="120" y="1430" class="text">• 多光谱rPPG</text>
  <text x="120" y="1450" class="text">• AI算法优化</text>
  <text x="120" y="1470" class="text">• 实时边缘计算</text>
  <text x="120" y="1490" class="text">• 多生理参数</text>

  <!-- SpO2 Applications -->
  <rect x="510" y="1250" width="380" height="260" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="700" y="1275" text-anchor="middle" class="subtitle">SpO2应用场景</text>

  <text x="530" y="1300" class="text">医疗应用:</text>
  <text x="550" y="1320" class="text">• 呼吸系统疾病</text>
  <text x="550" y="1340" class="text">• 睡眠呼吸暂停</text>
  <text x="550" y="1360" class="text">• 重症监护</text>
  <text x="550" y="1380" class="text">• 疾病筛查</text>

  <text x="530" y="1410" class="text">消费级应用:</text>
  <text x="550" y="1430" class="text">• 智能手表/手环</text>
  <text x="550" y="1450" class="text">• 运动健身监测</text>
  <text x="550" y="1470" class="text">• 家庭健康管理</text>
  <text x="550" y="1490" class="text">• 老年人监护</text>

  <!-- FMCW Radar Applications -->
  <rect x="940" y="1250" width="380" height="260" rx="5" fill="white" stroke="#bdc3c7" stroke-width="1"/>
  <text x="1130" y="1275" text-anchor="middle" class="subtitle">FMCW雷达应用</text>

  <text x="960" y="1300" class="text">医疗应用:</text>
  <text x="980" y="1320" class="text">• 非接触生命体征</text>
  <text x="980" y="1340" class="text">• 睡眠监测</text>
  <text x="980" y="1360" class="text">• 跌倒检测</text>
  <text x="980" y="1380" class="text">• 多人同时监测</text>

  <text x="960" y="1410" class="text">发展趋势:</text>
  <text x="980" y="1430" class="text">• 毫米波集成化</text>
  <text x="980" y="1450" class="text">• 深度学习算法</text>
  <text x="980" y="1470" class="text">• 穿墙检测</text>
  <text x="980" y="1490" class="text">• 车载健康监测</text>

  <!-- Arrows showing relationship -->
  <line x1="250" y1="210" x2="250" y2="240" class="arrow"/>
  <line x1="650" y1="210" x2="650" y2="240" class="arrow"/>
  <line x1="1150" y1="210" x2="1150" y2="240" class="arrow"/>

  <!-- Signal Visualization -->
  <text x="800" y="1540" text-anchor="middle" class="subtitle">典型信号波形对比</text>

  <!-- rPPG Signal -->
  <text x="200" y="1570" class="text">rPPG信号 (绿光通道)</text>
  <path d="M 50 1590 Q 100 1570 150 1590 Q 200 1610 250 1590 Q 300 1570 350 1590" class="signal-line ppg-signal"/>

  <!-- SpO2 Signals -->
  <text x="600" y="1570" class="text">SpO2双波长信号</text>
  <text x="600" y="1585" class="small-text">红光 (660nm)</text>
  <path d="M 500 1600 Q 550 1580 600 1600 Q 650 1620 700 1600 Q 750 1580 800 1600" class="signal-line red-light"/>
  <text x="600" y="1625" class="small-text">红外光 (940nm)</text>
  <path d="M 500 1640 Q 550 1625 600 1640 Q 650 1655 700 1640 Q 750 1625 800 1640" class="signal-line infrared-light"/>

  <!-- FMCW Radar Signal -->
  <text x="1100" y="1570" class="text">FMCW雷达信号</text>
  <text x="1100" y="1585" class="small-text">微多普勒频移</text>
  <path d="M 950 1600 Q 1000 1585 1050 1600 Q 1100 1615 1150 1600 Q 1200 1585 1250 1600" class="signal-line radar-signal"/>
  <text x="1100" y="1625" class="small-text">心跳 + 呼吸混合信号</text>
  <path d="M 950 1640 Q 975 1630 1000 1640 Q 1025 1650 1050 1640 Q 1075 1630 1100 1640 Q 1125 1650 1150 1640 Q 1175 1630 1200 1640 Q 1225 1650 1250 1640" class="signal-line radar-signal" stroke-dasharray="3,3"/>

  <!-- Technical Notes -->
  <text x="800" y="1680" text-anchor="middle" class="small-text">注: 三种技术各有优势，可在不同场景下互补使用，共同构建全面的生理监测系统</text>
</svg>
